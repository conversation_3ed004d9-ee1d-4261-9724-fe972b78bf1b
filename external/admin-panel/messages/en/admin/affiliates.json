{"admin": {"affiliates": {"title": "Affiliate Management", "subtitle": "Manage affiliate registrations and information", "navigation": {"affiliateManagement": "Affiliate Management"}, "actions": {"create": "Register New Affiliate", "edit": "Edit", "delete": "Delete", "view": "View", "save": "Save", "cancel": "Cancel", "back": "Back", "refresh": "Refresh", "search": "Search", "filter": "Filter", "export": "Export Excel", "clear": "Clear"}, "form": {"personalInformation": "Personal Information", "entityInformation": "Entity Information", "locationInformation": "Location Information", "additionalInformation": "Additional Information", "title": {"create": "Register New Affiliate", "edit": "Edit Affiliate Information", "view": "View Affiliate Information"}, "fields": {"name": {"label": "First Name", "placeholder": "Enter first name", "required": "First name is required", "minLength": "First name must be at least 2 characters"}, "family": {"label": "Last Name", "placeholder": "Enter last name", "required": "Last name is required", "minLength": "Last name must be at least 2 characters"}, "phone": {"label": "Mobile Number", "placeholder": "09123456789", "required": "Mobile number is required", "pattern": "Invalid mobile number"}, "email": {"label": "Email", "placeholder": "<EMAIL>", "pattern": "Invalid email address"}, "entity_type": {"label": "Entity Type", "placeholder": "Select entity type", "required": "Entity type is required", "options": {"PERSON": "Individual", "CORPORATION": "Corporation"}}, "registration_code": {"label": "Company Registration Code", "placeholder": "Enter company registration code", "required": "Registration code is required for corporations"}, "corporation_name": {"label": "Company Name", "placeholder": "Enter company name", "required": "Company name is required for corporations"}, "province_id": {"label": "Province", "placeholder": "Select province", "required": "Province selection is required"}, "city_id": {"label": "City", "placeholder": "Select city", "required": "City selection is required"}, "address": {"label": "Address", "placeholder": "Enter complete address", "required": "Address is required", "minLength": "Address must be at least 10 characters"}, "description": {"label": "Description", "placeholder": "Additional notes (optional)"}, "status": {"label": "Status", "placeholder": "Select status", "options": {"PENDING": "Pending Review", "AWAITING_CONFIRMATION": "Awaiting Confirmation", "ACTIVE": "Active", "DISABLED": "Disabled"}}}, "validation": {"required": "This field is required", "invalidEmail": "Invalid email address", "invalidPhone": "Invalid mobile number"}}, "table": {"headers": {"id": "ID", "name": "Name", "entity_type": "Entity Type", "phone": "Mobile", "email": "Email", "province": "Province", "city": "City", "status": "Status", "created_at": "Registration Date", "actions": "Actions"}, "empty": {"title": "No Affiliates Found", "description": "No affiliates have been registered in the system", "action": "Register New Affiliate"}, "loading": "Loading...", "error": "Error loading data"}, "pagination": {"itemsPerPage": "Affiliates per page", "showing": "Showing {start} to {end} of {total} affiliates", "previous": "Previous", "next": "Next", "page": "Page {page} of {totalPages}"}, "filters": {"title": "Filters", "entity_type": "Entity Type", "status": "Status", "province": "Province", "city": "City", "search": "Search by name, email or mobile", "searchPlaceholder": "Search by name, email or mobile number", "allEntityTypes": "All Entity Types", "allStatuses": "All Statuses", "apply": "Apply Filters", "clear": "Clear Filters"}, "messages": {"success": {"created": "Affiliate registered successfully", "updated": "Affiliate information updated successfully", "deleted": "Affiliate deleted successfully"}, "error": {"create": "Error registering affiliate", "update": "Error updating affiliate information", "delete": "Error deleting affiliate", "fetch": "Error fetching affiliate data", "network": "Network connection error", "timeout": "Request timeout - please try again"}, "confirm": {"delete": {"title": "Delete Affiliate", "message": "Are you sure you want to delete this affiliate? This action cannot be undone.", "confirm": "Yes, Delete", "cancel": "Cancel"}}, "submit": {"success": "Affiliate registered successfully", "error": "Error registering affiliate"}}, "stats": {"total": "Total Affiliates", "active": "Active", "pending": "Pending", "disabled": "Disabled"}, "provinces": {"loading": "Loading provinces...", "error": "Error loading provinces"}, "cities": {"loading": "Loading cities...", "error": "Error loading cities"}}}}