{"affiliate": {"registration": {"title": "Affiliate Registration", "subtitle": "Complete the form below to start your partnership with us", "description": "By completing this form, your partnership request will be registered and we will contact you after review.", "form": {"personalInformation": "Personal Information", "entityInformation": "Entity Information", "locationInformation": "Location Information", "additionalInformation": "Additional Information", "fields": {"name": {"label": "First Name", "placeholder": "Enter your first name", "required": "First name is required", "minLength": "First name must be at least 2 characters"}, "family": {"label": "Last Name", "placeholder": "Enter your last name", "required": "Last name is required", "minLength": "Last name must be at least 2 characters"}, "phone": {"label": "Mobile Number", "placeholder": "09********9", "required": "Mobile number is required", "pattern": "Invalid mobile number", "description": "Mobile number will be used for contact and sending information"}, "email": {"label": "Email", "placeholder": "<EMAIL>", "pattern": "Invalid email address", "description": "Email is optional but will be useful for receiving information"}, "entity_type": {"label": "Entity Type", "placeholder": "Select entity type", "required": "Entity type is required", "description": "Specify your entity type", "options": {"PERSON": "Individual", "CORPORATION": "Corporation"}}, "registration_code": {"label": "Company Registration Code", "placeholder": "Enter company registration code", "required": "Company registration code is required for corporations", "description": "Official company registration code from the companies registration office"}, "corporation_name": {"label": "Company Name", "placeholder": "Enter company name", "required": "Company name is required for corporations", "description": "Official company name according to articles of incorporation"}, "province_id": {"label": "Province", "placeholder": "Select province", "required": "Province selection is required"}, "city_id": {"label": "City", "placeholder": "Select city", "required": "City selection is required"}, "address": {"label": "Address", "placeholder": "Enter your complete address", "required": "Address is required", "minLength": "Address must be at least 10 characters", "description": "Complete address of your residence or business location"}, "description": {"label": "Description", "placeholder": "Additional information about yourself or your business (optional)", "description": "Any additional information you think would be helpful"}}, "validation": {"invalidEmail": "Invalid email address", "phonePattern": "Mobile number must start with 09 and be 11 digits", "required": "This field is required", "minLength": "Enter at least {min} characters", "maxLength": "Maximum {max} characters allowed"}}, "actions": {"submit": "Submit Partnership Request", "submitting": "Submitting...", "cancel": "Cancel", "reset": "Clear Form", "backToLogin": "Back to Login"}, "messages": {"success": {"title": "Your request has been successfully submitted!", "description": "Your partnership request has been received and is pending review. After approval, we will contact you via mobile number or email.", "nextSteps": "Next steps:", "step1": "Review of information by our team", "step2": "Contact to complete the process", "step3": "Partnership account activation", "estimatedTime": "Estimated review time: 2-3 business days"}, "error": {"title": "Error submitting request", "description": "Unfortunately, an error occurred while submitting your request. Please try again.", "tryAgain": "Try Again", "contactSupport": "If the problem persists, please contact support"}, "validation": {"phoneExists": "This mobile number is already registered", "emailExists": "This email is already registered", "invalidData": "The entered data is invalid", "networkError": "Server connection error - please check your internet connection"}}, "help": {"title": "Help", "entityTypeHelp": "If you are registering as an individual, select Individual. If you have a company, select Corporation.", "phoneHelp": "Mobile number must be valid as it will be used for identity verification.", "addressHelp": "Enter complete and accurate address so we can contact you if needed.", "supportPhone": "021-********", "supportEmail": "<EMAIL>"}}}}