// Affiliate DTOs based on Swagger API documentation

export interface CreateAffiliateDto {
  name: string; // Required for PERSON entity type
  family: string; // Required for PERSON entity type
  phone: string; // Required
  email?: string; // Optional
  entity_type: "PERSON" | "CORPORATION";
  registration_code?: string; // Required for CORPORATION entity type
  corporation_name?: string; // Required for CORPORATION entity type
  province_id: number; // Required
  city_id: number; // Required
  address: string; // Required
  description?: string; // Optional
  status?: "PENDING" | "AWAITING_CONFIRMATION" | "ACTIVE" | "DISABLED"; // Admin only
}

export interface UpdateAffiliateDto {
  name: string; // Required for PERSON entity type
  family: string; // Required for PERSON entity type
  email?: string; // Optional
  entity_type: "PERSON" | "CORPORATION";
  registration_code?: string; // Required for CORPORATION entity type
  corporation_name?: string; // Required for CORPORATION entity type
  province_id: number;
  city_id: number;
  address: string;
  description?: string; // Optional
}

export interface AffiliateResponseDto {
  id: number;
  entity_type: "PERSON" | "CORPORATION";
  registration_code?: string;
  corporation_name?: string;
  province_id: number;
  city_id: number;
  address: string;
  description?: string;
  user_id: number;
  created_at: string; // ISO date-time format
  updated_at: string; // ISO date-time format
  province?: {
    id: number;
    title: string;
  };
  city?: {
    id: number;
    title: string;
  };
  // User information (from the related user)
  user?: {
    id: number;
    name: string;
    family: string;
    phone: string;
    email: string;
    status: "PENDING" | "AWAITING_CONFIRMATION" | "ACTIVE" | "DISABLED";
  };
}

export interface AffiliateListResponseDto {
  data: AffiliateResponseDto[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form data types for the frontend
export interface AffiliateFormData {
  name: string;
  family: string;
  phone: string;
  email: string;
  entity_type: "PERSON" | "CORPORATION";
  registration_code: string;
  corporation_name: string;
  province_id: number | null;
  city_id: number | null;
  address: string;
  description: string;
  status?: "PENDING" | "AWAITING_CONFIRMATION" | "ACTIVE" | "DISABLED";
}

// Form validation types
export interface AffiliateFormErrors {
  name?: string;
  family?: string;
  phone?: string;
  email?: string;
  entity_type?: string;
  registration_code?: string;
  corporation_name?: string;
  province_id?: string;
  city_id?: string;
  address?: string;
  description?: string;
  status?: string;
}

// API response types
export interface CreateAffiliateResponse {
  success: boolean;
  message: string;
  data?: AffiliateResponseDto;
}

export interface UpdateAffiliateResponse {
  success: boolean;
  message: string;
  data?: AffiliateResponseDto;
}

export interface GetAffiliateResponse {
  success: boolean;
  message?: string;
  data?: AffiliateResponseDto;
}

export interface GetAffiliatesResponse {
  success: boolean;
  message?: string;
  data?: AffiliateListResponseDto;
}

export interface DeleteAffiliateResponse {
  success: boolean;
  message: string;
}

// Pagination parameters
export interface AffiliatePaginationParams {
  page?: number;
  limit?: number;
}

// Province and City types for location selection
export interface Province {
  id: number;
  title: string;
}

export interface City {
  id: number;
  title: string;
  province_id: number;
}

// Filter parameters (if needed for future enhancements)
export interface AffiliateFilters {
  entity_type?: "PERSON" | "CORPORATION";
  status?: "PENDING" | "AWAITING_CONFIRMATION" | "ACTIVE" | "DISABLED";
  province_id?: number;
  city_id?: number;
  search?: string; // For searching by name, email, phone, etc.
}

// Combined filter and pagination parameters
export interface AffiliateQueryParams
  extends AffiliateFilters,
    AffiliatePaginationParams {}

// Utility types for form state management
export type AffiliateFormMode = "create" | "edit" | "view";

export interface AffiliateFormState {
  mode: AffiliateFormMode;
  data: AffiliateFormData;
  errors: AffiliateFormErrors;
  isLoading: boolean;
  isSubmitting: boolean;
}
