import { api, AppApiError } from "../api-client";
import {
  CreateAffiliateDto,
  UpdateAffiliateDto,
  AffiliateResponseDto,
  AffiliateListResponseDto,
  CreateAffiliateResponse,
  UpdateAffiliateResponse,
  GetAffiliateResponse,
  GetAffiliatesResponse,
  DeleteAffiliateResponse,
  AffiliateQueryParams,
  Province,
  City,
  AffiliateRegistrationDto,
  AffiliateRegistrationResponse,
} from "./affiliate-types";

/**
 * Create a new affiliate registration (for admin use)
 * @param affiliateData - The affiliate data to create
 * @returns Promise with the created affiliate or error
 */
export async function createAffiliate(
  affiliateData: CreateAffiliateDto
): Promise<CreateAffiliateResponse> {
  try {
    console.log("Creating affiliate with data:", affiliateData);

    const response = await api.postJson<AffiliateResponseDto>(
      `/affiliates`,
      affiliateData
    );

    return {
      success: true,
      message: "همکار با موفقیت ثبت شد",
      data: response,
    };
  } catch (error) {
    console.error("Create affiliate error:", error);

    let errorMessage = "خطا در ثبت همکار";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Register a new affiliate (public registration)
 * @param registrationData - The affiliate registration data
 * @returns Promise with the registration result or error
 */
export async function registerAffiliate(
  registrationData: AffiliateRegistrationDto
): Promise<AffiliateRegistrationResponse> {
  try {
    console.log("Registering affiliate with data:", registrationData);

    const response = await api.postJson<AffiliateResponseDto>(
      `/affiliates`,
      registrationData
    );

    return {
      success: true,
      message: "درخواست همکاری شما با موفقیت ثبت شد و در انتظار بررسی است",
      data: response,
    };
  } catch (error) {
    console.error("Register affiliate error:", error);

    let errorMessage = "خطا در ثبت درخواست همکاری";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Get all affiliates with filtering and pagination (admin only)
 * @param params - Query parameters for filtering and pagination
 * @returns Promise with the paginated affiliate list or error
 */
export async function getAffiliates(
  params: AffiliateQueryParams = {}
): Promise<GetAffiliatesResponse> {
  try {
    let url = `/affiliates`;

    // Add query parameters
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        searchParams.append(key, value.toString());
      }
    });

    if (searchParams.toString()) {
      url += `?${searchParams.toString()}`;
    }

    console.log("Fetching affiliates with URL:", url);

    const response = await api.getJson<AffiliateListResponseDto>(url);

    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error("Get affiliates error:", error);

    let errorMessage = "خطا در دریافت لیست همکاران";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Get a single affiliate by ID
 * @param id - The affiliate ID
 * @returns Promise with the affiliate data or error
 */
export async function getAffiliate(id: number): Promise<GetAffiliateResponse> {
  try {
    console.log("Fetching affiliate with ID:", id);

    const response = await api.getJson<AffiliateResponseDto>(
      `/affiliates/${id}`
    );

    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error("Get affiliate error:", error);

    let errorMessage = "خطا در دریافت اطلاعات همکار";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Update an existing affiliate (admin only)
 * @param id - The affiliate ID
 * @param affiliateData - The affiliate data to update
 * @returns Promise with the updated affiliate or error
 */
export async function updateAffiliate(
  id: number,
  affiliateData: UpdateAffiliateDto
): Promise<UpdateAffiliateResponse> {
  try {
    console.log("Updating affiliate with ID:", id, "Data:", affiliateData);

    const response = await api.putJson<AffiliateResponseDto>(
      `/affiliates/${id}`,
      affiliateData
    );

    return {
      success: true,
      message: "اطلاعات همکار با موفقیت بروزرسانی شد",
      data: response,
    };
  } catch (error) {
    console.error("Update affiliate error:", error);

    let errorMessage = "خطا در بروزرسانی اطلاعات همکار";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Delete an affiliate (admin only)
 * @param id - The affiliate ID
 * @returns Promise with success status or error
 */
export async function deleteAffiliate(
  id: number
): Promise<DeleteAffiliateResponse> {
  try {
    console.log("Deleting affiliate with ID:", id);

    await api.deleteJson(`/affiliates/${id}`);

    return {
      success: true,
      message: "همکار با موفقیت حذف شد",
    };
  } catch (error) {
    console.error("Delete affiliate error:", error);

    let errorMessage = "خطا در حذف همکار";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Get list of provinces
 * @returns Promise with provinces array or error
 */
export async function getProvinces(): Promise<{
  success: boolean;
  data?: Province[];
  message?: string;
}> {
  try {
    console.log("Fetching provinces");

    const response = await api.getJson<Province[]>("/provinces");

    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error("Get provinces error:", error);

    let errorMessage = "خطا در دریافت لیست استان‌ها";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}

/**
 * Get cities for a specific province
 * @param provinceId - The province ID
 * @returns Promise with cities array or error
 */
export async function getCities(
  provinceId: number
): Promise<{ success: boolean; data?: City[]; message?: string }> {
  try {
    console.log("Fetching cities for province ID:", provinceId);

    const response = await api.getJson<City[]>(
      `/provinces/${provinceId}/cities`
    );

    return {
      success: true,
      data: response,
    };
  } catch (error) {
    console.error("Get cities error:", error);

    let errorMessage = "خطا در دریافت لیست شهرها";

    if (error instanceof Error) {
      if (error.name === "AbortError") {
        errorMessage = "درخواست منقضی شد - لطفاً دوباره تلاش کنید";
      } else if (error.message.includes("fetch")) {
        errorMessage = "خطا در اتصال به سرور";
      } else if (error instanceof AppApiError) {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
}
