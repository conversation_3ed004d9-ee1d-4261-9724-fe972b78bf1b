import { useState, useEffect, useCallback } from "react";
import { City } from "../affiliate-types";
import { getCities } from "../affiliate-api";

interface UseCitiesReturn {
  data: City[] | null;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
}

export function useCities(provinceId: number): UseCitiesReturn {
  const [data, setData] = useState<City[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchCities = useCallback(async () => {
    if (!provinceId || provinceId <= 0) {
      setData(null);
      setLoading(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await getCities(provinceId);
      
      if (result.success && result.data) {
        setData(result.data);
      } else {
        setError(new Error(result.message || "Failed to fetch cities"));
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
    } finally {
      setLoading(false);
    }
  }, [provinceId]);

  const refetch = useCallback(() => {
    fetchCities();
  }, [fetchCities]);

  useEffect(() => {
    fetchCities();
  }, [fetchCities]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}
