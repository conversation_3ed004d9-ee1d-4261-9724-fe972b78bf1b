import { useState, useEffect, useCallback } from "react";
import { Province } from "../affiliate-types";
import { getProvinces } from "../affiliate-api";

interface UseProvincesReturn {
  data: Province[] | null;
  loading: boolean;
  error: Error | null;
  refetch: () => void;
}

export function useProvinces(): UseProvincesReturn {
  const [data, setData] = useState<Province[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchProvinces = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getProvinces();
      
      if (result.success && result.data) {
        setData(result.data);
      } else {
        setError(new Error(result.message || "Failed to fetch provinces"));
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Unknown error"));
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(() => {
    fetchProvinces();
  }, [fetchProvinces]);

  useEffect(() => {
    fetchProvinces();
  }, [fetchProvinces]);

  return {
    data,
    loading,
    error,
    refetch,
  };
}
