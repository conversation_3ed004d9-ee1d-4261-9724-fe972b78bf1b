"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AffiliateRegistrationDto } from "@/lib/shared/affiliate-types";
import { useTranslations } from "next-intl";
import { Loader2, Save, X, CheckCircle, AlertCircle } from "lucide-react";
import { useProvinces } from "@/lib/shared/hooks/useProvinces";
import { useCities } from "@/lib/shared/hooks/useCities";
import { toast } from "sonner";

interface AffiliateRegistrationFormProps {
  onSubmit: (data: AffiliateRegistrationDto) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  showSuccessMessage?: boolean;
  successMessage?: string;
}

export function AffiliateRegistrationForm({
  onSubmit,
  onCancel,
  isLoading = false,
  showSuccessMessage = false,
  successMessage,
}: AffiliateRegistrationFormProps) {
  const t = useTranslations("affiliate.registration");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const personSchema = z.object({
    entity_type: z.literal("PERSON"),
    registration_code: z.string().optional(),
    corporation_name: z.string().optional(),
  });

  const corpSchema = z.object({
    entity_type: z.literal("CORPORATION"),
    registration_code: z
      .string()
      .min(1, { message: t("form.fields.registration_code.required") }),
    corporation_name: z
      .string()
      .min(1, { message: t("form.fields.corporation_name.required") }),
  });

  const baseSchema = z.object({
    name: z
      .string()
      .min(1, { message: t("form.fields.name.required") })
      .min(2, { message: t("form.fields.name.minLength") }),
    family: z
      .string()
      .min(1, { message: t("form.fields.family.required") })
      .min(2, { message: t("form.fields.family.minLength") }),
    phone: z
      .string()
      .min(1, { message: t("form.fields.phone.required") })
      .regex(/^0?9[0-9]{9}$/, { message: t("form.fields.phone.pattern") }),
    email: z
      .string()
      .email({ message: t("form.validation.invalidEmail") })
      .optional()
      .or(z.literal("")),
    province_id: z.coerce
      .number()
      .min(1, { message: t("form.fields.province_id.required") }),
    city_id: z.coerce
      .number()
      .min(1, { message: t("form.fields.city_id.required") }),
    address: z
      .string()
      .min(1, { message: t("form.fields.address.required") })
      .min(10, { message: t("form.fields.address.minLength") }),
    description: z.string().optional(),
  });

  const schema = baseSchema
    .and(z.discriminatedUnion("entity_type", [personSchema, corpSchema]))
    .refine((data) => data.city_id > 0 && data.province_id > 0, {
      message: t("form.fields.city_id.required"),
      path: ["city_id"],
    });

  type FormData = z.infer<typeof schema>;

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      family: "",
      phone: "",
      email: "",
      entity_type: "PERSON",
      registration_code: "",
      corporation_name: "",
      province_id: 0,
      city_id: 0,
      address: "",
      description: "",
    } as FormData,
  });

  const { watch, setValue, reset } = form;
  const entityType = watch("entity_type");
  const provinceId = watch("province_id");

  const {
    data: provinces,
    loading: provincesLoading,
    error: provincesError,
    refetch: refetchProvinces,
  } = useProvinces();
  const {
    data: cities,
    loading: citiesLoading,
    error: citiesError,
    refetch: refetchCities,
  } = useCities(provinceId);

  // Toast errors for provinces and cities
  useEffect(() => {
    if (provincesError) {
      toast.error(provincesError.message || t("messages.error.description"));
    }
  }, [provincesError, t]);

  useEffect(() => {
    if (citiesError) {
      toast.error(citiesError.message || t("messages.error.description"));
    }
  }, [citiesError, t]);

  // Reset city when province changes
  useEffect(() => {
    if (provinceId > 0) {
      setValue("city_id", 0);
    }
  }, [provinceId, setValue]);

  const handleSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      // Convert form data to registration DTO
      const registrationData: AffiliateRegistrationDto = {
        name: data.name,
        family: data.family,
        phone: data.phone,
        email: data.email || undefined,
        entity_type: data.entity_type,
        registration_code: data.registration_code || undefined,
        corporation_name: data.corporation_name || undefined,
        province_id: data.province_id,
        city_id: data.city_id,
        address: data.address,
        description: data.description || undefined,
      };

      await onSubmit(registrationData);
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error(t("messages.error.description"));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    reset();
    setValue("province_id", 0);
    setValue("city_id", 0);
  };

  if (showSuccessMessage) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
            <div>
              <h2 className="text-2xl font-bold text-green-700 mb-2">
                {t("messages.success.title")}
              </h2>
              <p className="text-gray-600 mb-4">
                {successMessage || t("messages.success.description")}
              </p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg text-left">
              <h3 className="font-semibold text-green-800 mb-2">
                {t("messages.success.nextSteps")}
              </h3>
              <ol className="list-decimal list-inside space-y-1 text-green-700">
                <li>{t("messages.success.step1")}</li>
                <li>{t("messages.success.step2")}</li>
                <li>{t("messages.success.step3")}</li>
              </ol>
              <p className="text-sm text-green-600 mt-3">
                {t("messages.success.estimatedTime")}
              </p>
            </div>
            {onCancel && (
              <Button onClick={onCancel} variant="outline">
                {t("actions.backToLogin")}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-2xl text-center">
          {t("title")}
        </CardTitle>
        <p className="text-center text-gray-600">
          {t("subtitle")}
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Personal Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.personalInformation")}
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.name.label")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("form.fields.name.placeholder")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="family"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.family.label")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("form.fields.family.placeholder")}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.phone.label")}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("form.fields.phone.placeholder")}
                          dir="ltr"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("form.fields.phone.description")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.email.label")}</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder={t("form.fields.email.placeholder")}
                          dir="ltr"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        {t("form.fields.email.description")}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Entity Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.entityInformation")}
              </h3>

              <FormField
                control={form.control}
                name="entity_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("form.fields.entity_type.label")}</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue
                            placeholder={t("form.fields.entity_type.placeholder")}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="PERSON">
                          {t("form.fields.entity_type.options.PERSON")}
                        </SelectItem>
                        <SelectItem value="CORPORATION">
                          {t("form.fields.entity_type.options.CORPORATION")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      {t("form.fields.entity_type.description")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {entityType === "CORPORATION" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="registration_code"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("form.fields.registration_code.label")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("form.fields.registration_code.placeholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("form.fields.registration_code.description")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="corporation_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t("form.fields.corporation_name.label")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("form.fields.corporation_name.placeholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          {t("form.fields.corporation_name.description")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            {/* Location Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.locationInformation")}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="province_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.province_id.label")}</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        value={field.value?.toString() || ""}
                        disabled={provincesLoading}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                provincesLoading
                                  ? "در حال بارگذاری..."
                                  : t("form.fields.province_id.placeholder")
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {provinces?.map((province) => (
                            <SelectItem
                              key={province.id}
                              value={province.id.toString()}
                            >
                              {province.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="city_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.city_id.label")}</FormLabel>
                      <Select
                        onValueChange={(value) => field.onChange(Number(value))}
                        value={field.value?.toString() || ""}
                        disabled={citiesLoading || !provinceId}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue
                              placeholder={
                                !provinceId
                                  ? "ابتدا استان را انتخاب کنید"
                                  : citiesLoading
                                  ? "در حال بارگذاری..."
                                  : t("form.fields.city_id.placeholder")
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {cities?.map((city) => (
                            <SelectItem
                              key={city.id}
                              value={city.id.toString()}
                            >
                              {city.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("form.fields.address.label")}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t("form.fields.address.placeholder")}
                        className="min-h-[80px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("form.fields.address.description")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Additional Information Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.additionalInformation")}
              </h3>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("form.fields.description.label")}</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={t("form.fields.description.placeholder")}
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {t("form.fields.description.description")}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6">
              <Button
                type="submit"
                className="flex-1"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting || isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("actions.submitting")}
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    {t("actions.submit")}
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={isSubmitting || isLoading}
              >
                {t("actions.reset")}
              </Button>

              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting || isLoading}
                >
                  <X className="mr-2 h-4 w-4" />
                  {t("actions.cancel")}
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
