import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTranslations } from "next-intl";
import { useState, useMemo } from "react";
import { AffiliateQueryParams } from "@/lib/admin/affiliate-types";

interface AffiliateFiltersProps {
  onRefresh: () => void;
  loading: boolean;
  onFiltersChange?: (filters: Partial<AffiliateQueryParams>) => void;
  initialFilters?: Partial<AffiliateQueryParams>;
}

export function AffiliateFilters({
  onRefresh,
  loading,
  onFiltersChange,
  initialFilters = {},
}: AffiliateFiltersProps) {
  const t = useTranslations("admin.affiliates");
  const initialFiltersMemo = useMemo(
    () => ({
      entity_type: initialFilters.entity_type,
      status: initialFilters.status,
      search: initialFilters.search || "",
    }),
    [initialFilters.entity_type, initialFilters.status, initialFilters.search]
  );

  const [filters, setFilters] =
    useState<Partial<AffiliateQueryParams>>(initialFiltersMemo);

  const handleFilterChange = (
    key: keyof AffiliateQueryParams,
    value: string | undefined
  ) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleRefresh = () => {
    onRefresh();
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      entity_type: undefined,
      status: undefined,
      search: "",
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
    onRefresh();
  };

  return (
    <div className="bg-white p-4 rounded-lg border mb-6">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">{t("filters.title")}</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            <Button
              onClick={handleClearFilters}
              variant="outline"
              disabled={loading}
            >
              {t("filters.clear")}
            </Button>
            <Button
              onClick={handleRefresh}
              variant="outline"
              disabled={loading}
            >
              {t("actions.refresh")}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label htmlFor="entity-type-filter">
              {t("filters.entity_type")}
            </Label>
            <Select
              value={filters.entity_type || ""}
              onValueChange={(value) =>
                handleFilterChange(
                  "entity_type",
                  value === "all" ? undefined : value
                )
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("filters.allEntityTypes")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  {t("filters.allEntityTypes")}
                </SelectItem>
                <SelectItem value="PERSON">
                  {t("form.fields.entity_type.options.PERSON")}
                </SelectItem>
                <SelectItem value="CORPORATION">
                  {t("form.fields.entity_type.options.CORPORATION")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="status-filter">{t("filters.status")}</Label>
            <Select
              value={filters.status || ""}
              onValueChange={(value) =>
                handleFilterChange(
                  "status",
                  value === "all" ? undefined : value
                )
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("filters.allStatuses")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("filters.allStatuses")}</SelectItem>
                <SelectItem value="PENDING">
                  {t("form.fields.status.options.PENDING")}
                </SelectItem>
                <SelectItem value="AWAITING_CONFIRMATION">
                  {t("form.fields.status.options.AWAITING_CONFIRMATION")}
                </SelectItem>
                <SelectItem value="ACTIVE">
                  {t("form.fields.status.options.ACTIVE")}
                </SelectItem>
                <SelectItem value="DISABLED">
                  {t("form.fields.status.options.DISABLED")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="search-filter">{t("filters.search")}</Label>
            <Input
              id="search-filter"
              placeholder={t("filters.searchPlaceholder")}
              value={filters.search || ""}
              onChange={(e) => handleFilterChange("search", e.target.value)}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
