"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AffiliateTable } from "./AffiliateTable";
import { AffiliateMobileCards } from "./AffiliateMobileCards";
import { AffiliatePagination } from "./AffiliatePagination";
import { AffiliateFilters } from "./AffiliateFilters";
import { getAffiliates, deleteAffiliate } from "@/lib/admin/affiliate-api";
import {
  AffiliateResponseDto,
  AffiliateQueryParams,
} from "@/lib/admin/affiliate-types";
import { useTranslations } from "next-intl";
import { Plus, AlertCircle } from "lucide-react";
import { toast } from "sonner";

interface AffiliateContentProps {
  initialFilters: AffiliateQueryParams;
  onFiltersChange: (filters: AffiliateQueryParams) => void;
}

export function AffiliateContent({
  initialFilters,
  onFiltersChange,
}: AffiliateContentProps) {
  const t = useTranslations("admin.affiliates");
  const router = useRouter();

  // State management
  const [affiliates, setAffiliates] = useState<AffiliateResponseDto[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [affiliateToDelete, setAffiliateToDelete] =
    useState<AffiliateResponseDto | null>(null);

  // Local filters state synced with props
  const [localFilters, setLocalFilters] =
    useState<AffiliateQueryParams>(initialFilters);

  // Sync local filters when initialFilters change
  useEffect(() => {
    setLocalFilters(initialFilters);
  }, [initialFilters]);

  // Pagination state derived from API response
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);

  // Load affiliates data
  const loadAffiliates = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const params: AffiliateQueryParams = {
        page: localFilters.page ?? 1,
        limit: localFilters.limit ?? 10,
      };

      if (localFilters.search) {
        params.search = localFilters.search;
      }
      if (localFilters.status) {
        params.status = localFilters.status as
          | "PENDING"
          | "AWAITING_CONFIRMATION"
          | "ACTIVE"
          | "DISABLED";
      }
      if (localFilters.entity_type) {
        params.entity_type = localFilters.entity_type as
          | "PERSON"
          | "CORPORATION";
      }

      const result = await getAffiliates(params);

      if (result.success && result.data) {
        setAffiliates(result.data.data);
        setTotalItems(result.data.total);
        setTotalPages(result.data.totalPages);
      } else {
        setError(result.message || t("messages.error.fetch"));
      }
    } catch (err) {
      console.error("Error loading affiliates:", err);
      setError(t("messages.error.network"));
    } finally {
      setIsLoading(false);
    }
  }, [localFilters, t]);

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadAffiliates();
  }, [loadAffiliates]);

  // Handle affiliate actions
  const handleCreate = () => {
    router.push("/admin/affiliates/new");
  };

  const handleEdit = (affiliate: AffiliateResponseDto) => {
    router.push(`/admin/affiliates/${affiliate.id}/edit`);
  };

  const handleDelete = (affiliate: AffiliateResponseDto) => {
    setAffiliateToDelete(affiliate);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!affiliateToDelete) return;

    try {
      const result = await deleteAffiliate(affiliateToDelete.id);

      if (result.success) {
        toast.success(result.message);
        await loadAffiliates();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast.error(t("messages.error.delete"));
    } finally {
      setDeleteDialogOpen(false);
      setAffiliateToDelete(null);
    }
  };

  const handleRefresh = () => {
    loadAffiliates();
  };

  const handleFiltersChange = (newFilters: Partial<AffiliateQueryParams>) => {
    const updated = { ...localFilters, ...newFilters, page: 1 };
    setLocalFilters(updated);
    onFiltersChange(updated);
  };

  const handlePageChange = (page: number) => {
    const newFilters = { ...localFilters, page };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleItemsPerPageChange = (items: number) => {
    const newFilters = { ...localFilters, limit: items, page: 1 };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Show list view
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">{t("title")}</h1>
          <p className="text-gray-600">{t("subtitle")}</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleCreate} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t("actions.create")}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <AffiliateFilters
        onRefresh={handleRefresh}
        loading={isLoading}
        onFiltersChange={handleFiltersChange}
        initialFilters={localFilters}
      />

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Affiliates List */}
      <Card>
        <CardContent className="p-0">
          <AffiliateTable
            affiliates={affiliates}
            onEdit={handleEdit}
            onDelete={handleDelete}
            isLoading={isLoading}
          />
          <AffiliateMobileCards
            affiliates={affiliates}
            onEdit={handleEdit}
            onDelete={handleDelete}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalItems > 0 && (
        <AffiliatePagination
          paginationData={{
            data: affiliates,
            page: localFilters.page ?? 1,
            totalPages,
            total: totalItems,
            limit: localFilters.limit ?? 10,
          }}
          onPageChange={handlePageChange}
          onPageSizeChange={handleItemsPerPageChange}
          loading={isLoading}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("messages.confirm.delete.title")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("messages.confirm.delete.message")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>
              {t("messages.confirm.delete.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              {t("messages.confirm.delete.confirm")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
