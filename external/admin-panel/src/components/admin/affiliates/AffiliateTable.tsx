"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDate } from "@/lib/date-utils";
import { AffiliateResponseDto } from "@/lib/admin/affiliate-types";
import { useTranslations } from "next-intl";
import { MoreHorizontal, Edit, Trash2 } from "lucide-react";

interface AffiliateTableProps {
  affiliates: AffiliateResponseDto[];
  onEdit?: (affiliate: AffiliateResponseDto) => void;
  onDelete?: (affiliate: AffiliateResponseDto) => void;
  isLoading?: boolean;
}

export function AffiliateTable({
  affiliates,
  onEdit,
  onDelete,
  isLoading = false,
}: AffiliateTableProps) {
  const t = useTranslations("admin.affiliates");

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-100 text-green-800";
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "AWAITING_CONFIRMATION":
        return "bg-blue-100 text-blue-800";
      case "DISABLED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getEntityTypeLabel = (entityType: string) => {
    return t(`form.fields.entity_type.options.${entityType}`);
  };

  const getStatusLabel = (status: string) => {
    return t(`form.fields.status.options.${status}`);
  };

  const getFullName = (affiliate: AffiliateResponseDto) => {
    if (affiliate.user) {
      return `${affiliate.user.name} ${affiliate.user.family}`;
    }
    return "-";
  };

  const getContactInfo = (affiliate: AffiliateResponseDto) => {
    if (affiliate.user) {
      return {
        phone: affiliate.user.phone,
        email: affiliate.user.email,
      };
    }
    return { phone: "-", email: "-" };
  };

  if (isLoading) {
    return (
      <div className="hidden md:block p-5">
        <div className="animate-pulse">
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-100 rounded mb-2"></div>
          ))}
        </div>
      </div>
    );
  }

  if (affiliates.length === 0) {
    return (
      <div className="hidden md:block">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {t("table.empty.title")}
          </h3>
          <p className="text-gray-500 mb-4">{t("table.empty.description")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="hidden md:block overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>{t("table.headers.id")}</TableHead>
            <TableHead>{t("table.headers.name")}</TableHead>
            <TableHead>{t("table.headers.entity_type")}</TableHead>
            <TableHead>{t("table.headers.phone")}</TableHead>
            <TableHead>{t("table.headers.email")}</TableHead>
            <TableHead>{t("table.headers.province")}</TableHead>
            <TableHead>{t("table.headers.city")}</TableHead>
            <TableHead>{t("table.headers.status")}</TableHead>
            <TableHead>{t("table.headers.created_at")}</TableHead>
            <TableHead className="w-[100px]">
              {t("table.headers.actions")}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {affiliates.map((affiliate) => {
            const contactInfo = getContactInfo(affiliate);
            const status = affiliate.user?.status || "PENDING";

            return (
              <TableRow key={affiliate.id}>
                <TableCell className="font-medium">{affiliate.id}</TableCell>
                <TableCell>
                  <div className="font-medium">{getFullName(affiliate)}</div>
                  {affiliate.entity_type === "CORPORATION" && (
                    <div className="text-sm text-gray-500">
                      {affiliate.corporation_name}
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  {getEntityTypeLabel(affiliate.entity_type)}
                </TableCell>
                <TableCell className="font-mono text-sm">
                  {contactInfo.phone}
                </TableCell>
                <TableCell className="text-sm">{contactInfo.email}</TableCell>
                <TableCell>{affiliate.province?.title || "-"}</TableCell>
                <TableCell>{affiliate.city?.title || "-"}</TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                      status
                    )}`}
                  >
                    {getStatusLabel(status)}
                  </span>
                </TableCell>
                <TableCell className="text-sm">
                  {formatDate(affiliate.created_at)}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {onEdit && (
                        <DropdownMenuItem onClick={() => onEdit(affiliate)}>
                          <Edit className="mr-2 h-4 w-4" />
                          {t("actions.edit")}
                        </DropdownMenuItem>
                      )}
                      {onDelete && (
                        <DropdownMenuItem
                          onClick={() => onDelete(affiliate)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          {t("actions.delete")}
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
