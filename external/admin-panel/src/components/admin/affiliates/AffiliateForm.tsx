"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AffiliateResponseDto,
  AffiliateFormMode,
  CreateAffiliateDto,
} from "@/lib/admin/affiliate-types";
import { useTranslations } from "next-intl";
import { Loader2, Save, X, ArrowLeft } from "lucide-react";
import { useProvinces } from "@/hooks/admin/useProvinces";
import { useCities } from "@/hooks/admin/useCities";
import { toast } from "sonner";

interface AffiliateFormProps {
  mode: AffiliateFormMode;
  initialData?: AffiliateResponseDto;
  onSubmit: (data: CreateAffiliateDto) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function AffiliateForm({
  mode,
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
}: AffiliateFormProps) {
  const t = useTranslations("admin.affiliates");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const personSchema = z.object({
    entity_type: z.literal("PERSON"),
    registration_code: z.string().optional(),
    corporation_name: z.string().optional(),
  });

  const corpSchema = z.object({
    entity_type: z.literal("CORPORATION"),
    registration_code: z
      .string()
      .min(1, { message: t("form.fields.registration_code.required") }),
    corporation_name: z
      .string()
      .min(1, { message: t("form.fields.corporation_name.required") }),
  });

  const baseSchema = z.object({
    name: z
      .string()
      .min(1, { message: t("form.fields.name.required") })
      .min(2, { message: t("form.fields.name.minLength") }),
    family: z
      .string()
      .min(1, { message: t("form.fields.family.required") })
      .min(2, { message: t("form.fields.family.minLength") }),
    phone: z
      .string()
      .min(1, { message: t("form.fields.phone.required") })
      .regex(/^0?9[0-9]{9}$/, { message: t("form.fields.phone.pattern") }),
    email: z
      .string()
      .email({ message: t("form.validation.invalidEmail") })
      .optional()
      .or(z.literal("")),
    province_id: z.coerce
      .number()
      .min(1, { message: t("form.fields.province_id.required") }),
    city_id: z.coerce
      .number()
      .min(1, { message: t("form.fields.city_id.required") }),
    address: z
      .string()
      .min(1, { message: t("form.fields.address.required") })
      .min(10, { message: t("form.fields.address.minLength") }),
    description: z.string().optional(),
    status: z
      .enum(["PENDING", "AWAITING_CONFIRMATION", "ACTIVE", "DISABLED"])
      .optional(),
  });

  const schema = baseSchema
    .and(z.discriminatedUnion("entity_type", [personSchema, corpSchema]))
    .refine((data) => data.city_id > 0 && data.province_id > 0, {
      message: t("form.fields.city_id.required"),
      path: ["city_id"],
    });

  type FormData = z.infer<typeof schema>;

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: "",
      family: "",
      phone: "",
      email: "",
      entity_type: "PERSON",
      registration_code: "",
      corporation_name: "",
      province_id: 0,
      city_id: 0,
      address: "",
      description: "",
      status: "ACTIVE",
    } as FormData,
  });

  const { watch, setValue, reset } = form;
  const entityType = watch("entity_type");
  const provinceId = watch("province_id");

  const {
    data: provinces,
    loading: provincesLoading,
    error: provincesError,
    refetch: refetchProvinces,
  } = useProvinces();
  const {
    data: cities,
    loading: citiesLoading,
    error: citiesError,
    refetch: refetchCities,
  } = useCities(provinceId);

  // Initialize form with existing data
  useEffect(() => {
    if (initialData && mode !== "create") {
      const formData = {
        name: initialData.user?.name || "",
        family: initialData.user?.family || "",
        phone: initialData.user?.phone || "",
        email: initialData.user?.email || "",
        entity_type: initialData.entity_type,
        registration_code: initialData.registration_code || "",
        corporation_name: initialData.corporation_name || "",
        province_id: initialData.province_id,
        city_id: initialData.city_id,
        address: initialData.address,
        description: initialData.description || "",
        status: initialData.user?.status || "PENDING",
      };
      // reset all form fields first
      reset(formData);

      // Ensure province and city option lists are loaded so Select can display the prefilled values.
      // Refetch provinces (in case the list wasn't loaded) and then refetch cities for the province.
      // After lists are available, explicitly set province_id and city_id to guarantee Select components show them.
      refetchProvinces();
      if (formData.province_id) {
        // set the province so the cities hook picks it up
        setValue("province_id", formData.province_id);
        // load cities for this province
        refetchCities();
        // preserve city selection if present
        if (formData.city_id) {
          setValue("city_id", formData.city_id);
        }
      } else {
        // no province provided: clear city as well
        setValue("city_id", 0);
      }
    }
  }, [initialData, mode, reset, refetchProvinces, refetchCities, setValue]);

  // Toast errors for provinces and cities
  useEffect(() => {
    if (provincesError) {
      toast.error(provincesError.message || t("provinces.error"));
    }
  }, [provincesError, t]);

  useEffect(() => {
    if (citiesError) {
      toast.error(citiesError.message || t("cities.error"));
    }
  }, [citiesError, t]);

  // Reset city when province changes.
  // Avoid clearing a prefilled city during initial form initialization:
  // - On first effect run (initial mount) we want to load cities for the prefilled province
  //   but preserve the city_id that was provided by initialData.
  // - On subsequent changes (user selects a different province) we reset city_id to 0
  //   so the user must explicitly pick a new city.
  useEffect(() => {
    // track whether this is the first time provinceId effect runs
    let isInitial = true;
    // IIFE to allow async flows or early returns if needed in future
    (function handleProvinceChange() {
      if (!provinceId || provinceId === 0) {
        // No province selected: clear cities and stop
        setValue("city_id", 0);
        refetchCities();
        return;
      }
      if (isInitial) {
        // First run after mount/initialization:
        // fetch cities but preserve any existing city_id that came from initialData/reset
        refetchCities();
        isInitial = false;
        return;
      }
      // Subsequent runs (user changed province): clear selected city and fetch list
      setValue("city_id", 0);
      refetchCities();
    })();
    // note: provinceId, setValue and refetchCities are in deps array below
  }, [provinceId, setValue, refetchCities]);

  // When cities are loaded, ensure a prefilled city from initialData is applied.
  // initialData.city_id may be set before the cities list is available; wait for cities
  // to load and only then set the city value so the Select can find the matching option.
  useEffect(() => {
    if (
      initialData &&
      mode !== "create" &&
      typeof initialData.city_id === "number" &&
      initialData.city_id > 0 &&
      Array.isArray(cities) &&
      cities.length > 0
    ) {
      const found = cities.some((c) => c.id === initialData.city_id);
      if (found) {
        setValue("city_id", initialData.city_id);
      }
    }
  }, [initialData, mode, cities, setValue]);

  const onFormSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      await onSubmit(data);
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : t("messages.submit.error")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const isReadOnly = mode === "view";

  const getTitle = () => {
    return t(`form.title.${mode}`);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-2xl">{getTitle()}</CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="flex items-center gap-2"
            disabled={isLoading}
          >
            <ArrowLeft className="h-4 w-4" />
            {t("actions.back")}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onFormSubmit)}
            className="space-y-6"
          >
            {/* Personal Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.personalInformation")}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.name.label")} *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder={t("form.fields.name.placeholder")}
                          disabled={isReadOnly || isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="family"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.family.label")} *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder={t("form.fields.family.placeholder")}
                          disabled={isReadOnly || isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.phone.label")} *</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder={t("form.fields.phone.placeholder")}
                          type="tel"
                          disabled={isReadOnly || isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.email.label")}</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder={t("form.fields.email.placeholder")}
                          type="email"
                          disabled={isReadOnly || isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* Entity Type */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.entityInformation")}
              </h3>
              <FormField
                control={form.control}
                name="entity_type"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>
                      {t("form.fields.entity_type.label")} *
                    </FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger disabled={isReadOnly || isLoading}>
                          <SelectValue
                            placeholder={t(
                              "form.fields.entity_type.placeholder"
                            )}
                          />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="PERSON">
                          {t("form.fields.entity_type.options.PERSON")}
                        </SelectItem>
                        <SelectItem value="CORPORATION">
                          {t("form.fields.entity_type.options.CORPORATION")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {entityType === "CORPORATION" && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="registration_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("form.fields.registration_code.label")} *
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t(
                                "form.fields.registration_code.placeholder"
                              )}
                              disabled={isReadOnly || isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="corporation_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            {t("form.fields.corporation_name.label")} *
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder={t(
                                "form.fields.corporation_name.placeholder"
                              )}
                              disabled={isReadOnly || isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </>
              )}
            </div>

            {/* Location */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.locationInformation")}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="province_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {t("form.fields.province_id.label")} *
                      </FormLabel>
                      <Select
                        onValueChange={(value) =>
                          setValue("province_id", Number(value))
                        }
                        value={field.value?.toString() || ""}
                      >
                        <FormControl>
                          <SelectTrigger
                            className="w-full"
                            disabled={
                              isReadOnly ||
                              isLoading ||
                              provincesLoading ||
                              !!provincesError
                            }
                          >
                            {provincesLoading ? (
                              <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                {t("provinces.loading")}
                              </div>
                            ) : provincesError ? (
                              t("provinces.error")
                            ) : (
                              <SelectValue
                                placeholder={t(
                                  "form.fields.province_id.placeholder"
                                )}
                              />
                            )}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {provinces?.map((province) => (
                            <SelectItem
                              key={province.id}
                              value={province.id.toString()}
                            >
                              {province.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="city_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.city_id.label")} *</FormLabel>
                      <Select
                        onValueChange={(value) =>
                          setValue("city_id", Number(value))
                        }
                        value={field.value?.toString() || ""}
                        disabled={
                          !provinceId ||
                          isReadOnly ||
                          isLoading ||
                          citiesLoading ||
                          !!citiesError
                        }
                      >
                        <FormControl>
                          <SelectTrigger
                            className="w-full"
                            disabled={
                              !provinceId ||
                              isReadOnly ||
                              isLoading ||
                              citiesLoading ||
                              !!citiesError
                            }
                          >
                            {citiesLoading ? (
                              <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                {t("cities.loading")}
                              </div>
                            ) : citiesError ? (
                              t("cities.error")
                            ) : !provinceId ? (
                              t("form.fields.city_id.placeholder") // or "First select province"
                            ) : (
                              <SelectValue
                                placeholder={t(
                                  "form.fields.city_id.placeholder"
                                )}
                              />
                            )}
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {cities?.map((city) => (
                            <SelectItem
                              key={city.id}
                              value={city.id.toString()}
                            >
                              {city.title}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("form.fields.address.label")} *</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder={t("form.fields.address.placeholder")}
                        disabled={isReadOnly || isLoading}
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Additional Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold border-b pb-2">
                {t("form.additionalInformation")}
              </h3>
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t("form.fields.description.label")}</FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder={t("form.fields.description.placeholder")}
                        disabled={isReadOnly || isLoading}
                        className="min-h-[80px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {!isReadOnly && (
                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("form.fields.status.label")}</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger disabled={isLoading}>
                            <SelectValue
                              placeholder={
                                t("form.fields.status.placeholder") ||
                                "Select status"
                              }
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="PENDING">
                            {t("form.fields.status.options.PENDING")}
                          </SelectItem>
                          <SelectItem value="AWAITING_CONFIRMATION">
                            {t(
                              "form.fields.status.options.AWAITING_CONFIRMATION"
                            )}
                          </SelectItem>
                          <SelectItem value="ACTIVE">
                            {t("form.fields.status.options.ACTIVE")}
                          </SelectItem>
                          <SelectItem value="DISABLED">
                            {t("form.fields.status.options.DISABLED")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            {/* Form Actions */}
            {!isReadOnly && (
              <div className="flex justify-end gap-4 pt-6 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading || isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  {t("actions.cancel")}
                </Button>
                <Button
                  type="submit"
                  disabled={
                    isLoading ||
                    isSubmitting ||
                    provincesLoading ||
                    citiesLoading
                  }
                  className="flex items-center gap-2"
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  {t("actions.save")}
                </Button>
              </div>
            )}
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
