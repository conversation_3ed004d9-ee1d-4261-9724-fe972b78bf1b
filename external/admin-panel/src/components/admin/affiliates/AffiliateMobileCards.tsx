"use client";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDate } from "@/lib/date-utils";
import { AffiliateResponseDto } from "@/lib/admin/affiliate-types";
import { useTranslations } from "next-intl";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";

interface AffiliateMobileCardsProps {
  affiliates: AffiliateResponseDto[];
  onEdit?: (affiliate: AffiliateResponseDto) => void;
  onDelete?: (affiliate: AffiliateResponseDto) => void;
  isLoading?: boolean;
}

export function AffiliateMobileCards({
  affiliates,
  onEdit,
  onDelete,
  isLoading = false,
}: AffiliateMobileCardsProps) {
  const t = useTranslations("admin.affiliates");

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "default";
      case "PENDING":
        return "secondary";
      case "AWAITING_CONFIRMATION":
        return "outline";
      case "DISABLED":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getEntityTypeLabel = (entityType: string) => {
    return t(`form.fields.entity_type.options.${entityType}`);
  };

  const getStatusLabel = (status: string) => {
    return t(`form.fields.status.options.${status}`);
  };

  const getFullName = (affiliate: AffiliateResponseDto) => {
    if (affiliate.user) {
      return `${affiliate.user.name} ${affiliate.user.family}`;
    }
    return "-";
  };

  const getContactInfo = (affiliate: AffiliateResponseDto) => {
    if (affiliate.user) {
      return {
        phone: affiliate.user.phone,
        email: affiliate.user.email,
      };
    }
    return { phone: "-", email: "-" };
  };

  if (isLoading) {
    return (
      <div className="md:hidden space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-3">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (affiliates.length === 0) {
    return (
      <div className="md:hidden">
        <Card>
          <CardContent className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t("table.empty.title")}
            </h3>
            <p className="text-gray-500 mb-4">{t("table.empty.description")}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="md:hidden space-y-4">
      {affiliates.map((affiliate) => {
        const contactInfo = getContactInfo(affiliate);
        const status = affiliate.user?.status || "PENDING";

        return (
          <Card key={affiliate.id}>
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-lg">
                      {getFullName(affiliate)}
                    </h3>
                    <Badge
                      variant={getStatusVariant(status)}
                      className="text-xs"
                    >
                      {getStatusLabel(status)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <span>#{affiliate.id}</span>
                    <span>•</span>
                    <span>{getEntityTypeLabel(affiliate.entity_type)}</span>
                  </div>
                  {affiliate.entity_type === "CORPORATION" && (
                    <div className="text-sm text-gray-600 mt-1">
                      {affiliate.corporation_name}
                    </div>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onEdit && (
                      <DropdownMenuItem onClick={() => onEdit(affiliate)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t("actions.edit")}
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <DropdownMenuItem
                        onClick={() => onDelete(affiliate)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t("actions.delete")}
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {/* Contact Information */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span className="font-mono">{contactInfo.phone}</span>
                  </div>
                  {contactInfo.email !== "-" && (
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-gray-400" />
                      <span>{contactInfo.email}</span>
                    </div>
                  )}
                </div>

                {/* Location */}
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span>
                    {affiliate.city?.title}, {affiliate.province?.title}
                  </span>
                </div>

                {/* Registration Date */}
                <div className="text-xs text-gray-500 pt-2 border-t">
                  {t("table.headers.created_at")}:{" "}
                  {formatDate(affiliate.created_at)}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
