"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

type NavItem = {
  href: string;
  label: string;
  ariaLabel?: string;
};

export type NavProps = {
  activeRoute?: string;
  variant?: "default" | "compact" | "collapsed";
  className?: string;
  items?: NavItem[];
};

/**
 * Dashboard navigation extracted from layout.
 *
 * Uses the repository's NavigationMenu primitives (shadcn style wrappers)
 * Preserves styling and active-state via data-active attribute applied to links.
 *
 * Accessible: links are focusable, keyboard navigable, and focus-visible styles are preserved
 *
 * Client component because it uses next/navigation hook for pathname.
 */
export default function AdminNav({
  activeRoute,
  variant = "default",
}: NavProps) {
  const pathname =
    usePathname?.() ??
    activeRoute ??
    (typeof window !== "undefined"
      ? window.location.pathname
      : "/admin/dashboard");
  const compact = variant === "compact";

  const t = useTranslations("admin.dashboard.navigation");
  const DEFAULT_ITEMS: NavItem[] = [
    { href: "/admin/dashboard", label: t("dashboard") },
    { href: "#", label: t("userModeration") },
    { href: "#", label: t("productManagement") },
    { href: "#", label: t("salesReports") },
    { href: "/admin/dashboard/discounts", label: t("discountCodes") },
    {
      href: "/admin/dashboard/generate-discount",
      label: t("generateDiscount"),
    },
    { href: "/admin/affiliates", label: t("affiliateManagement") },
  ];

  return (
    <div className="mx-auto">
      <nav className="w-full">
        <ul className="w-full flex flex-col md:flex-row space-x-4 py-4 items-stretch">
          {DEFAULT_ITEMS.map((item, index) => {
            const isActive = item.href !== "#" && pathname === item.href;
            // NavigationMenuLink expects an element with data-active attribute to style active state.
            return (
              <Link
                key={index}
                className={cn(
                  isActive
                    ? "text-gray-900 hover:text-blue-600"
                    : "text-gray-500 hover:text-blue-600",
                  compact ? "text-sm py-1" : "text-base py-1.5",
                  "focus:outline-none focus-visible:ring-2 focus-visible:ring-blue-300 rounded-sm",
                  "data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4"
                )}
                aria-current={isActive ? "page" : undefined}
                href={item.href}
                aria-label={item.ariaLabel ?? item.label}
                data-active={isActive ? "true" : "false"}
              >
                <li>{item.label}</li>
              </Link>
            );
          })}
        </ul>
      </nav>
    </div>
  );
}
