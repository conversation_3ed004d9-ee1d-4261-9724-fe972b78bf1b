"use client";

import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { UserRole } from "@/lib/auth-types";

interface RoleSelectorProps {
  userRoles: UserRole[];
}

export default function RoleSelector({ userRoles }: RoleSelectorProps) {
  const router = useRouter();
  const t = useTranslations("common");

  const handleRoleSelect = (role: UserRole, dashboardUrl: string) => {
    // Persist the selected role in sessionStorage
    sessionStorage.setItem("selectedRole", role);
    // Redirect to the chosen dashboard
    router.push(dashboardUrl);
  };

  const roleConfigs = [
    {
      role: "ADMIN" as UserRole,
      title: t("selectRole.adminTitle", { defaultMessage: "Admin Dashboard" }),
      description: t("selectRole.adminDescription", {
        defaultMessage: "Manage users, products, and discounts",
      }),
      url: "/admin/dashboard",
      icon: "👑", // Simple emoji or import icon if needed
    },
    {
      role: "AFFILIATE" as UserRole,
      title: t("selectRole.affiliateTitle", {
        defaultMessage: "Affiliate Dashboard",
      }),
      description: t("selectRole.affiliateDescription", {
        defaultMessage: "Track commissions and reports",
      }),
      url: "/affiliate/dashboard",
      icon: "💼",
    },
  ].filter((config) => userRoles.includes(config.role));

  if (roleConfigs.length === 0) {
    // Fallback if no valid roles
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-center">Access Denied</CardTitle>
            <CardDescription className="text-center">
              {t("selectRole.noRoles", {
                defaultMessage: "No valid roles found",
              })}
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <Button onClick={() => router.push("/403")}>
              {t("selectRole.goTo403", { defaultMessage: "Go to Error Page" })}
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md ">
        <CardHeader>
          <CardTitle className="text-center">
            {t("selectRole.title", { defaultMessage: "Select Your Dashboard" })}
          </CardTitle>
          <CardDescription className="text-center">
            {t("selectRole.description", {
              defaultMessage:
                "You have access to multiple dashboards. Choose one to continue.",
            })}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {roleConfigs.map((config) => (
            <Button
              key={config.role}
              variant="outline"
              className="w-full justify-start py-11"
              onClick={() => handleRoleSelect(config.role, config.url)}
            >
              <span className="mr-2">{config.icon}</span>
              <div className="flex flex-col items-start">
                <span className="font-medium">{config.title}</span>
                <span className="text-sm text-gray-600">
                  {config.description}
                </span>
              </div>
            </Button>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}
