import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
import { UserRole } from "./lib/auth-types";

// Route configurations
const adminRoutes = ["/dashboard", "/admin"];
const affiliateRoutes = ["/affiliate"];
const publicRoutes = [
  "/",
  "/login",
  "/api/auth",
  "/403",
  "/affiliate/register",
];

// Get user roles from NextAuth token and validate token expiration
async function getUserRolesAndValidateToken(request: NextRequest): Promise<{
  roles: UserRole[] | null;
  tokenExpired: boolean;
  shouldRefresh: boolean;
}> {
  try {
    console.log("Middleware - Attempting to get NextAuth token");

    // Try multiple approaches to get the token
    let token = null;

    // Method 1: Try getToken with different configurations
    try {
      token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET,
      });
      console.log("Middleware - getToken method 1 result:", !!token);
    } catch (error) {
      console.log("Middleware - getToken method 1 failed:", error);
    }

    // Method 2: Try with different cookie name
    if (!token) {
      try {
        token = await getToken({
          req: request,
          secret: process.env.NEXTAUTH_SECRET,
          cookieName: "next-auth.session-token",
        });
        console.log("Middleware - getToken method 2 result:", !!token);
      } catch (error) {
        console.log("Middleware - getToken method 2 failed:", error);
      }
    }

    // Method 3: Try with secure cookie name
    if (!token) {
      try {
        token = await getToken({
          req: request,
          secret: process.env.NEXTAUTH_SECRET,
          cookieName: "__Secure-next-auth.session-token",
        });
        console.log("Middleware - getToken method 3 result:", !!token);
      } catch (error) {
        console.log("Middleware - getToken method 3 failed:", error);
      }
    }

    console.log("Middleware - Final NextAuth token:", {
      hasToken: !!token,
      tokenKeys: token ? Object.keys(token) : null,
      roles: token?.roles,
      accessToken: token?.accessToken ? "present" : "missing",
    });

    if (!token) {
      console.log("Middleware - No NextAuth token found with any method");
      return { roles: null, tokenExpired: true, shouldRefresh: false };
    }

    // Check token expiration if we have an access token
    let tokenExpired = false;
    let shouldRefresh = false;

    if (token.accessToken) {
      try {
        const { isTokenExpired, isTokenExpiringSoon } = await import(
          "./lib/auth-types"
        );
        const accessToken = token.accessToken as string;

        tokenExpired = isTokenExpired(accessToken);
        shouldRefresh = tokenExpired || isTokenExpiringSoon(accessToken, 5);

        console.log("Middleware - Token validation:", {
          tokenExpired,
          shouldRefresh,
        });

        if (tokenExpired) {
          console.log("⚠️ Middleware - Access token is expired");
          return { roles: null, tokenExpired: true, shouldRefresh: true };
        }

        if (shouldRefresh) {
          console.log("⚠️ Middleware - Access token is expiring soon");
        }
      } catch (tokenCheckError) {
        console.error(
          "Middleware - Error checking token expiration:",
          tokenCheckError
        );
        // If we can't check expiration, assume token is valid but should be refreshed
        shouldRefresh = true;
      }
    }

    // First try to get roles directly from the NextAuth token
    if (token.roles && Array.isArray(token.roles)) {
      console.log("Middleware - Found roles in NextAuth token:", token.roles);
      return {
        roles: token.roles as UserRole[],
        tokenExpired,
        shouldRefresh,
      };
    }

    // If no roles in NextAuth token, try to decode the access token
    if (token.accessToken) {
      console.log(
        "Middleware - No roles in NextAuth token, trying access token"
      );
      try {
        const { decodeJwt } = await import("jose");
        const accessTokenPayload = decodeJwt(token.accessToken as string);
        console.log("Middleware - Decoded access token payload:", {
          id: accessTokenPayload.id,
          username: accessTokenPayload.username,
          roles: accessTokenPayload.roles,
        });

        if (
          accessTokenPayload.roles &&
          Array.isArray(accessTokenPayload.roles)
        ) {
          console.log(
            "Middleware - Found roles in access token:",
            accessTokenPayload.roles
          );
          return {
            roles: accessTokenPayload.roles as UserRole[],
            tokenExpired,
            shouldRefresh,
          };
        }
      } catch (accessTokenError) {
        console.error(
          "Middleware - Failed to decode access token:",
          accessTokenError
        );
      }
    }

    console.warn(
      "Middleware - No roles found in either NextAuth token or access token"
    );
    return { roles: null, tokenExpired, shouldRefresh };
  } catch (error) {
    console.error("Middleware - NextAuth token error:", error);
    return { roles: null, tokenExpired: true, shouldRefresh: false };
  }
}

// Check if user has required role for route
function hasRequiredRole(
  userRoles: UserRole[] | null,
  requiredRoles: UserRole[]
): boolean {
  if (!userRoles || userRoles.length === 0) return false;
  return requiredRoles.some((role) => userRoles.includes(role));
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  console.log("🔒 Middleware triggered for:", pathname);

  // Skip middleware for static files, API routes (except auth session calls), and Next.js internals
  if (
    pathname.startsWith("/_next/") ||
    (pathname.startsWith("/api/") &&
      !pathname.startsWith("/api/auth/session")) ||
    pathname.includes(".") ||
    publicRoutes.some(
      (route) => pathname === route || pathname.startsWith(`${route}/`)
    )
  ) {
    console.log("🔒 Middleware skipped for:", pathname);
    return NextResponse.next();
  }

  // Determine route type and required roles
  const isAdminRoute = adminRoutes.some(
    (route) => pathname === route || pathname.startsWith(`${route}/`)
  );
  const isAffiliateRoute =
    affiliateRoutes.some(
      (route) => pathname === route || pathname.startsWith(`${route}/`)
    ) && !pathname.startsWith("/affiliate/register"); // Exclude registration route

  console.log("🔒 Route analysis:", {
    pathname,
    adminRoutes,
    affiliateRoutes,
    isAdminRoute,
    isAffiliateRoute,
  });

  // If it's a protected route, check authentication and authorization
  if (isAdminRoute || isAffiliateRoute) {
    // Check if guards are enabled
    const enableAdminGuard =
      process.env.NEXT_PUBLIC_ENABLE_ADMIN_GUARD === "true";
    const enableRoleCheck =
      process.env.NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK === "true";

    console.log("🔒 Environment variables:", {
      NEXT_PUBLIC_ENABLE_ADMIN_GUARD:
        process.env.NEXT_PUBLIC_ENABLE_ADMIN_GUARD,
      NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK:
        process.env.NEXT_PUBLIC_ENABLE_ADMIN_ROLE_CHECK,
      enableAdminGuard,
      enableRoleCheck,
    });

    if (!enableAdminGuard) {
      console.log("🔒 Admin guard disabled, allowing access");
      return NextResponse.next();
    }

    try {
      console.log("Middleware - Route protection check:", {
        pathname,
        isAdminRoute,
        isAffiliateRoute,
        enableRoleCheck,
      });

      // If role checking is enabled, get user roles and verify access
      if (enableRoleCheck) {
        const tokenValidation = await getUserRolesAndValidateToken(request);
        const {
          roles: userRoles,
          tokenExpired,
          shouldRefresh,
        } = tokenValidation;

        // If token is expired, redirect to login
        if (tokenExpired) {
          console.log("Middleware - Token expired, redirecting to login");
          const loginUrl = new URL("/login", request.url);
          loginUrl.searchParams.set("returnUrl", pathname);
          loginUrl.searchParams.set("expired", "true");
          return NextResponse.redirect(loginUrl);
        }

        // If no roles found, redirect to login
        if (!userRoles) {
          console.log("Middleware - No user roles found, redirecting to login");
          const loginUrl = new URL("/login", request.url);
          loginUrl.searchParams.set("returnUrl", pathname);
          return NextResponse.redirect(loginUrl);
        }

        // If token should be refreshed, add a header to indicate this
        // The client-side components can pick this up and trigger a session refresh
        const response = NextResponse.next();
        if (shouldRefresh) {
          console.log("Middleware - Token should be refreshed, adding header");
          response.headers.set("X-Token-Refresh-Needed", "true");
        }

        if (isAdminRoute && !hasRequiredRole(userRoles, ["ADMIN"])) {
          console.log(
            "Middleware - Admin access denied, insufficient roles:",
            userRoles
          );
          const forbiddenUrl = new URL("/403", request.url);
          forbiddenUrl.searchParams.set("reason", "admin_required");
          return NextResponse.redirect(forbiddenUrl);
        }

        if (isAffiliateRoute && !hasRequiredRole(userRoles, ["AFFILIATE"])) {
          console.log(
            "Middleware - Affiliate access denied, insufficient roles:",
            userRoles
          );
          const forbiddenUrl = new URL("/403", request.url);
          forbiddenUrl.searchParams.set("reason", "affiliate_required");
          return NextResponse.redirect(forbiddenUrl);
        }

        console.log("Middleware - Role check passed:", {
          userRoles,
          isAdminRoute,
          isAffiliateRoute,
          shouldRefresh,
        });

        // Return the response with potential refresh header
        return response;
      }

      console.log("Middleware - Access granted");
    } catch (error) {
      console.error("Middleware auth error:", error);
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("returnUrl", pathname);
      return NextResponse.redirect(loginUrl);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    "/((?!api|_next/static|_next/image|favicon.ico).*)",
  ],
};
