import { getRequestConfig } from "next-intl/server";

export default getRequestConfig(async () => {
  // Static for now, we'll change this later
  const locale = "fa";
  const messages = {
    common: (await import(`../../messages/${locale}/common.json`)).common,
    auth: (await import(`../../messages/${locale}/auth.json`)).auth,
    admin: {
      dashboard: (await import(`../../messages/${locale}/admin/dashboard.json`))
        .admin.dashboard,
      users: (await import(`../../messages/${locale}/admin/users.json`)).admin
        .users,
      products: (await import(`../../messages/${locale}/admin/products.json`))
        .admin.products,
      discount: {
        generating: (
          await import(
            `../../messages/${locale}/admin/discount/generating.json`
          )
        ).admin.discount.generating,
        discounts: (
          await import(`../../messages/${locale}/admin/discount/discounts.json`)
        ).admin.discount.discounts,
      },
      affiliates: (
        await import(`../../messages/${locale}/admin/affiliates.json`)
      ).admin.affiliates,
    },
    affiliate: {
      dashboard: (
        await import(`../../messages/${locale}/affiliate/dashboard.json`)
      ).affiliate.dashboard,
      commissions: (
        await import(`../../messages/${locale}/affiliate/commissions.json`)
      ).affiliate.commissions,
      reports: (await import(`../../messages/${locale}/affiliate/reports.json`))
        .affiliate.reports,
    },
  };
  return {
    locale,
    messages,
  };
});
