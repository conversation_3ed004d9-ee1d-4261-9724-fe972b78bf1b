"use client";

import { useEffect, useMemo } from "react";
import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { hasRole, UserRole } from "@/lib/auth-types";
import Loading from "../admin/dashboard/loading";
import { getRoleBasedRedirectUrl } from "@/lib/role-based-navigation";
import RoleSelector from "@/components/role-selector";

/**
 * Generic dashboard page that redirects users to their role-specific dashboard
 * This acts as a router based on user roles
 */
export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const t = useTranslations("common");

  const userRoles = useMemo(() => session?.user?.roles || [], [session]);

  useEffect(() => {
    if (process.env.NODE_ENV !== "production") {
      console.log("Dashboard page - Auth check:", {
        status,
        hasSession: !!session,
        userRoles,
      });
    }

    if (status === "loading") return;

    if (!session) {
      if (process.env.NODE_ENV !== "production") {
        console.log("No session found, redirecting to login");
      }
      router.push("/login");
      return;
    }

    // Clear invalid selected role if it doesn't match current roles
    const selectedRole = sessionStorage.getItem(
      "selectedRole"
    ) as UserRole | null;
    if (selectedRole && !hasRole(userRoles, selectedRole)) {
      if (process.env.NODE_ENV !== "production") {
        console.log("Clearing invalid selected role:", selectedRole);
      }
      sessionStorage.removeItem("selectedRole");
    }

    if (userRoles.length === 0) {
      if (process.env.NODE_ENV !== "production") {
        console.log("No roles found, redirecting to 403");
      }
      router.push("/403?reason=no_roles");
      return;
    }

    // For single role, redirect
    if (userRoles.length === 1) {
      const redirectUrl = getRoleBasedRedirectUrl(userRoles);
      if (window.location.pathname !== redirectUrl) {
        if (process.env.NODE_ENV !== "production") {
          console.log("Dashboard redirect for single role:", redirectUrl);
        }
        router.push(redirectUrl);
      }
      return;
    }

    // For multiple roles, no redirect - selector will be shown
    if (process.env.NODE_ENV !== "production") {
      console.log("Multiple roles, showing selector");
    }
  }, [session, status, router, userRoles.length, userRoles]); // Removed userRoles from deps as it's now memoized and derived from session

  if (status === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loading />
          <p className="mt-4 text-gray-600">{t("loading")}</p>
        </div>
      </div>
    );
  }

  if (!session || userRoles.length === 0 || userRoles.length === 1) {
    // Show loading while redirecting
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Loading />
          <p className="mt-4 text-gray-600">
            {!session
              ? t("dashboard.redirectToLogin")
              : userRoles.length === 0
              ? t("dashboard.redirectToError")
              : t("dashboard.redirectToDashboard")}
          </p>
        </div>
      </div>
    );
  }

  // Multiple roles: show selector
  return <RoleSelector userRoles={userRoles} />;
}
