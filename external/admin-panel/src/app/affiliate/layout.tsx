import { SignOutButton } from "@/components/sign-out-button";
import { formatDate } from "@/lib/date-utils";
import { useTranslations } from "next-intl";
import AffiliateNav from "@/components/affiliate/affiliate-nav";
import AuthGuard from "@/components/auth-guard";

export default function AffiliateLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const t = useTranslations();

  return (
    <AuthGuard requiredRoles={["AFFILIATE"]}>
      <div className="min-h-screen bg-gray-50">
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {t("affiliate.dashboard.title")}
                </h1>
                <p className="text-sm text-gray-600">
                  {t("common.date")}: {formatDate(new Date())}
                </p>
              </div>
              <SignOutButton />
            </div>
            <AffiliateNav activeRoute="/affiliate" variant="default" />
          </div>
        </header>

        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {children}
        </main>
      </div>
    </AuthGuard>
  );
}
