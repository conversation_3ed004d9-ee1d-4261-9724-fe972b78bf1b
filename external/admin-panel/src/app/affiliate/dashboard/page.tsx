"use client";

import {
  Card,
  CardContent,
  CardDes<PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useTranslations } from "next-intl";

function AffiliateDashboardContent() {
  const t = useTranslations("affiliate.dashboard");
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900">
          داشبورد همکار          
        </h2>
        <p className="text-gray-600">
          مشاهده آمار و عملکرد همکاری شما
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>کل کمیسیون</CardTitle>
            <CardDescription>
              مجموع کمیسیون دریافتی
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۲,۵۰۰,۰۰۰ تومان</div>
            <p className="text-xs text-muted-foreground">
              +۲۰.۱% نسبت به ماه قبل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>تعداد ارجاعات</CardTitle>
            <CardDescription>
              کاربران ارجاع داده شده
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۱۲۳</div>
            <p className="text-xs text-muted-foreground">
              +۱۵% نسبت به ماه قبل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>فروش این ماه</CardTitle>
            <CardDescription>
              فروش از طریق لینک شما
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۸,۹۰۰,۰۰۰ تومان</div>
            <p className="text-xs text-muted-foreground">
              +۱۲% نسبت به ماه قبل
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>نرخ تبدیل</CardTitle>
            <CardDescription>
              درصد تبدیل بازدیدکنندگان
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">۳.۲%</div>
            <p className="text-xs text-muted-foreground">
              +۰.۵% نسبت به ماه قبل
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>آخرین ارجاعات</CardTitle>
            <CardDescription>
              کاربران اخیراً ارجاع داده شده
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { name: "احمد محمدی", date: "۱۴۰۳/۱۰/۱۵", amount: "۱۵۰,۰۰۰ تومان" },
                { name: "فاطمه احمدی", date: "۱۴۰۳/۱۰/۱۴", amount: "۲۳۰,۰۰۰ تومان" },
                { name: "علی رضایی", date: "۱۴۰۳/۱۰/۱۳", amount: "۱۸۰,۰۰۰ تومان" },
              ].map((referral, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{referral.name}</p>
                    <p className="text-sm text-gray-600">{referral.date}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{referral.amount}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>لینک ارجاع شما</CardTitle>
            <CardDescription>
              از این لینک برای ارجاع کاربران استفاده کنید
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-3 bg-gray-100 rounded-md">
                <code className="text-sm">
                  https://example.com/ref/ABC123
                </code>
              </div>
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  کپی لینک
                </button>
                <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">
                  اشتراک‌گذاری
                </button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function AffiliateDashboardPage() {
  return <AffiliateDashboardContent />;
}
