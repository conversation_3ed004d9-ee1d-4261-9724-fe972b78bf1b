"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { AffiliateRegistrationForm } from "@/components/shared/AffiliateRegistrationForm";
import { registerAffiliate } from "@/lib/shared/affiliate-api";
import { AffiliateRegistrationDto } from "@/lib/shared/affiliate-types";
import { toast } from "sonner";
import { ArrowLeft, Home } from "lucide-react";
import Link from "next/link";

export default function AffiliateRegistrationPage() {
  const router = useRouter();
  const t = useTranslations("affiliate.registration");
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const handleSubmit = async (data: AffiliateRegistrationDto) => {
    setIsLoading(true);
    try {
      console.log("Submitting affiliate registration:", data);

      const result = await registerAffiliate(data);

      if (result.success) {
        setSuccessMessage(result.message);
        setShowSuccess(true);
        toast.success(t("messages.success.title"));
      } else {
        toast.error(result.message || t("messages.error.description"));
      }
    } catch (error) {
      console.error("Registration submission error:", error);
      toast.error(t("messages.error.description"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push("/login");
  };

  const handleBackToLogin = () => {
    router.push("/login");
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Link
              href="/login"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t("actions.backToLogin")}
            </Link>

            <Link
              href="/"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <Home className="mr-2 h-4 w-4" />
              صفحه اصلی
            </Link>
          </div>

          {!showSuccess && (
            <div className="text-center max-w-2xl mx-auto">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {t("title")}
              </h1>
              <p className="text-lg text-gray-600 mb-2">{t("description")}</p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                <p className="text-blue-800 text-sm">
                  <strong>توجه:</strong> تکمیل تمام فیلدهای الزامی ضروری است. پس
                  از ثبت درخواست، تیم ما در اسرع وقت با شما تماس خواهد گرفت.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Registration Form */}
        <AffiliateRegistrationForm
          onSubmit={handleSubmit}
          onCancel={showSuccess ? handleBackToLogin : handleCancel}
          isLoading={isLoading}
          showSuccessMessage={showSuccess}
          successMessage={successMessage}
        />

        {/* Help Section */}
        {!showSuccess && (
          <div className="mt-12 max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center">
                <span className="bg-blue-100 text-blue-600 rounded-full p-2 mr-3">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </span>
                {t("help.title")}
              </h2>

              <div className="space-y-4 text-sm text-gray-600">
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">نوع نهاد:</h3>
                  <p>{t("help.entityTypeHelp")}</p>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-1">
                    شماره موبایل:
                  </h3>
                  <p>{t("help.phoneHelp")}</p>
                </div>

                <div>
                  <h3 className="font-medium text-gray-900 mb-1">آدرس:</h3>
                  <p>{t("help.addressHelp")}</p>
                </div>

                <div className="border-t pt-4 mt-4">
                  <h3 className="font-medium text-gray-900 mb-2">
                    تماس با پشتیبانی:
                  </h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="flex items-center">
                      <span className="text-gray-500 mr-2">تلفن:</span>
                      <a
                        href={`tel:${t("help.supportPhone")}`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                        dir="ltr"
                      >
                        {t("help.supportPhone")}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <span className="text-gray-500 mr-2">ایمیل:</span>
                      <a
                        href={`mailto:${t("help.supportEmail")}`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                        dir="ltr"
                      >
                        {t("help.supportEmail")}
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <footer className="mt-16 text-center text-sm text-gray-500">
          <p>
            © {new Date().getFullYear()} تمامی حقوق محفوظ است. با ثبت‌نام، شما
            با{" "}
            <a href="#" className="text-blue-600 hover:text-blue-800">
              قوانین و مقررات
            </a>{" "}
            ما موافقت می‌کنید.
          </p>
        </footer>
      </div>
    </div>
  );
}
