"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createDiscount } from "@/lib/admin/discount-api";
import {
  DiscountFormData,
  DiscountFormErrors,
} from "@/lib/admin/discount-types";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { DateObject } from "react-multi-date-picker";
import gregorian from "react-date-object/calendars/gregorian";
import gregorian_en from "react-date-object/locales/gregorian_en";
import CustomDatePicker from "@/components/ui/date-picker";

function GenerateDiscountContent() {
  const t = useTranslations("admin.discount.generating");
  const { data: session } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [messageType, setMessageType] = useState<"success" | "error" | "">("");

  const [formData, setFormData] = useState<DiscountFormData>({
    expireDate: null,
    prefix: null,
    discountPercent: 0,
    count: 1,
    type: "simple",
    referralId: 0,
    description: "",
  });

  const [errors, setErrors] = useState<DiscountFormErrors>({});

  const validateForm = (): boolean => {
    const newErrors: DiscountFormErrors = {};

    // Validate expire date
    if (!formData.expireDate) {
      newErrors.expireDate = t("validation.required");
    }

    // Validate discount percent
    if (
      !formData.discountPercent ||
      formData.discountPercent < 1 ||
      formData.discountPercent > 100
    ) {
      newErrors.discountPercent = t("validation.invalidPercent");
    }

    // Validate count
    if (!formData.count || formData.count < 1) {
      newErrors.count = t("validation.invalidCount");
    }

    // Validate type
    if (!formData.type) {
      newErrors.type = t("validation.required");
    }

    // Validate referral ID for referral type
    if (
      formData.type === "referral" &&
      (!formData.referralId || formData.referralId < 1)
    ) {
      newErrors.referralId = t("validation.invalidReferralId");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!session?.accessToken) {
      setMessage("خطا در احراز هویت");
      setMessageType("error");
      return;
    }

    setLoading(true);
    setMessage("");
    setMessageType("");

    try {
      const discountData = {
        expire_date: formData
          .expireDate!.convert(gregorian, gregorian_en)
          .format("YYYY-MM-DD"),
        prefix: formData.prefix,
        discount_percent: formData.discountPercent,
        count: formData.count,
        type: formData.type,
        referral_id: formData.referralId,
        description: formData.description,
      };

      const result = await createDiscount(discountData);
      if (result.success) {
        setMessage(result.message);
        setMessageType("success");
        // Reset form
        setFormData({
          expireDate: null,
          prefix: null,
          discountPercent: 0,
          count: 1,
          type: "simple",
          referralId: 0,
          description: "",
        });
        setErrors({});
      } else {
        setMessage(result.message);
        setMessageType("error");
      }
    } catch (error) {
      console.error("Submit error:", error);
      setMessage(t("messages.error"));
      setMessageType("error");
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFormData({
      expireDate: null,
      prefix: null,
      discountPercent: 0,
      count: 1,
      type: "simple",
      referralId: 0,
      description: "",
    });
    setErrors({});
    setMessage("");
    setMessageType("");
  };

  const handleCancel = () => {
    router.push("/admin/dashboard");
  };

  return (
    <div className="px-4 py-6 sm:px-0">
      <Card>
        <CardHeader>
          <CardTitle>{t("title")}</CardTitle>
          <CardDescription>{t("subtitle")}</CardDescription>
        </CardHeader>
        <CardContent>
          {message && (
            <div
              className={`mb-4 p-3 rounded-md text-sm ${
                messageType === "success"
                  ? "bg-green-50 text-green-800 border border-green-200"
                  : "bg-red-50 text-red-800 border border-red-200"
              }`}
            >
              {message}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Expire Date */}
              <div className="space-y-2 w-full">
                <Label htmlFor="expireDate" isRequired>
                  {t("form.expireDate.label")}
                </Label>
                <CustomDatePicker
                  value={formData.expireDate}
                  onChange={(date: DateObject | null) =>
                    setFormData({
                      ...formData,
                      expireDate: date,
                    })
                  }
                  minDate={new DateObject()}
                  placeholder={t("form.expireDate.placeholder")}
                  error={!!errors.expireDate}
                  id="expireDate"
                />
                {errors.expireDate && (
                  <p className="text-red-600 text-sm">{errors.expireDate}</p>
                )}
              </div>

              {/* Prefix */}
              <div className="space-y-2">
                <Label htmlFor="prefix">{t("form.prefix.label")}</Label>

                <Input
                  id="prefix"
                  value={formData.prefix ?? ""}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      prefix: e.target.value,
                    })
                  }
                />
                {formData.prefix && (
                  <span className="text-xs text-muted-foreground">
                    {t("form.prefix.example", {
                      prefix: formData.prefix + "-GXDFZ",
                    })}
                  </span>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Discount Percent */}
              <div className="space-y-2">
                <Label htmlFor="discountPercent" isRequired>
                  {t("form.discountPercent.label")}
                </Label>
                <Input
                  id="discountPercent"
                  type="number"
                  min="1"
                  max="100"
                  value={formData.discountPercent || ""}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      discountPercent: parseInt(e.target.value) || 0,
                    })
                  }
                  placeholder={t("form.discountPercent.placeholder")}
                  className={errors.discountPercent ? "border-red-500" : ""}
                />
                {errors.discountPercent && (
                  <p className="text-red-600 text-sm">
                    {errors.discountPercent}
                  </p>
                )}
              </div>

              {/* Count */}
              <div className="space-y-2">
                <Label htmlFor="count">{t("form.count.label")}</Label>
                <Input
                  id="count"
                  type="number"
                  min="1"
                  value={formData.count || ""}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      count: parseInt(e.target.value) || 1,
                    })
                  }
                  placeholder={t("form.count.placeholder")}
                  className={errors.count ? "border-red-500" : ""}
                />
                {errors.count && (
                  <p className="text-red-600 text-sm">{errors.count}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Type */}
              <div className="space-y-2">
                <Label htmlFor="type">{t("form.type.label")}</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) =>
                    setFormData({
                      ...formData,
                      type: value as "simple" | "referral",
                    })
                  }
                >
                  <SelectTrigger
                    className={`w-full ${errors.type ? "border-red-500" : ""}`}
                  >
                    <SelectValue placeholder={t("form.type.placeholder")} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="simple">
                      {t("form.type.options.simple")}
                    </SelectItem>
                    <SelectItem value="referral">
                      {t("form.type.options.referral")}
                    </SelectItem>
                  </SelectContent>
                </Select>
                {errors.type && (
                  <p className="text-red-600 text-sm">{errors.type}</p>
                )}
              </div>

              {/* Referral ID */}
              <div className="space-y-2">
                <Label htmlFor="referralId">{t("form.referralId.label")}</Label>
                <Input
                  id="referralId"
                  type="number"
                  min="0"
                  value={formData.referralId || ""}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      referralId: parseInt(e.target.value) || 0,
                    })
                  }
                  placeholder={t("form.referralId.placeholder")}
                  className={errors.referralId ? "border-red-500" : ""}
                  disabled={formData.type === "simple"}
                />
                {errors.referralId && (
                  <p className="text-red-600 text-sm">{errors.referralId}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">{t("form.description.label")}</Label>
              <Input
                id="description"
                type="text"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder={t("form.description.placeholder")}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && (
                <p className="text-red-600 text-sm">{errors.description}</p>
              )}
            </div>

            {/* Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-6">
              <Button type="submit" disabled={loading} className="flex-1">
                {loading ? t("messages.loading") : t("buttons.submit")}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleReset}
                disabled={loading}
              >
                {t("buttons.reset")}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={loading}
              >
                {t("buttons.cancel")}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export default function GenerateDiscountPage() {
  return <GenerateDiscountContent />;
}
