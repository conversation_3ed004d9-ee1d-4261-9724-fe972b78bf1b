"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { AffiliateContent } from "@/components/admin/affiliates/AffiliateContent";
import { AffiliateQueryParams } from "@/lib/admin/affiliate-types";

function AffiliatesPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Parse URL parameters into initial filters
  const getInitialFilters = (): AffiliateQueryParams => {
    const params = new URLSearchParams(searchParams.toString());
    return {
      search: params.get("search") || undefined,
      status:
        (params.get("status") as
          | "PENDING"
          | "AWAITING_CONFIRMATION"
          | "ACTIVE"
          | "DISABLED") || undefined,
      entity_type:
        (params.get("entity_type") as "PERSON" | "CORPORATION") || undefined,
      page: params.get("page") ? parseInt(params.get("page")!) : 1,
      limit: params.get("limit") ? parseInt(params.get("limit")!) : 10,
    };
  };

  // Update URL when filters change
  const handleFiltersChange = (filters: Partial<AffiliateQueryParams>) => {
    const params = new URLSearchParams();

    // Add filter parameters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== "") {
        params.set(key, value.toString());
      }
    });

    // Update URL without triggering a page reload
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.history.replaceState({}, "", newUrl);
  };

  return (
    <AffiliateContent
      initialFilters={getInitialFilters()}
      onFiltersChange={handleFiltersChange}
    />
  );
}

export default function AffiliatesPage() {
  return <AffiliatesPageContent />;
}
