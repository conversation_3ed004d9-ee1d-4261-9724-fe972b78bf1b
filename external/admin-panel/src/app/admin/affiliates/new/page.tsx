"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { AffiliateForm } from "@/components/admin/affiliates/AffiliateForm";
import { createAffiliate } from "@/lib/admin/affiliate-api";
import { CreateAffiliateDto } from "@/lib/admin/affiliate-types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

export default function NewAffiliatePage() {
  const router = useRouter();
  const t = useTranslations("admin.affiliates");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: CreateAffiliateDto) => {
    setIsLoading(true);
    try {
      const result = await createAffiliate(data);

      if (result.success) {
        toast.success(t("messages.submit.success"));
        router.push("/admin/affiliates");
      } else {
        toast.error(result.message || t("messages.submit.error"));
      }
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error(t("messages.submit.error"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/affiliates");
  };

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle>{t("form.title.create")}</CardTitle>
        </CardHeader>
        <CardContent>
          <AffiliateForm
            mode="create"
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>
    </div>
  );
}
