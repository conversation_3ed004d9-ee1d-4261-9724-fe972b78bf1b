"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { AffiliateForm } from "@/components/admin/affiliates/AffiliateForm";
import { getAffiliate, updateAffiliate } from "@/lib/admin/affiliate-api";
import {
  CreateAffiliateDto,
  AffiliateResponseDto,
} from "@/lib/admin/affiliate-types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

export default function EditAffiliatePage() {
  const router = useRouter();
  const params = useParams();
  const affiliateId = params.id as string;
  const t = useTranslations("admin.affiliates");

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [affiliateData, setAffiliateData] =
    useState<AffiliateResponseDto | null>(null);

  // Load affiliate data
  useEffect(() => {
    const loadAffiliate = async () => {
      if (!affiliateId) return;

      setIsLoadingData(true);
      try {
        const result = await getAffiliate(parseInt(affiliateId));
        if (result.success && result.data) {
          setAffiliateData(result.data);
        } else {
          toast.error(result.message || t("messages.error.fetch"));
          router.push("/admin/affiliates");
        }
      } catch (error) {
        console.error("Error loading affiliate:", error);
        toast.error(t("messages.error.network"));
        router.push("/admin/affiliates");
      } finally {
        setIsLoadingData(false);
      }
    };

    loadAffiliate();
  }, [affiliateId, router, t]);

  const handleSubmit = async (data: CreateAffiliateDto) => {
    if (!affiliateData) return;

    setIsLoading(true);
    try {
      const result = await updateAffiliate(affiliateData.id, data);

      if (result.success) {
        toast.success(result.message);
        router.push("/admin/affiliates");
      } else {
        toast.error(result.message || t("messages.error.update"));
      }
    } catch (error) {
      console.error("Form submission error:", error);
      toast.error(t("messages.error.network"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/affiliates");
  };

  if (isLoadingData) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p>{t("table.loading")}</p>
          </div>
        </div>
      </div>
    );
  }

  if (!affiliateData) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <p>{t("messages.error.fetch")}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <Card>
        <CardHeader>
          <CardTitle>{t("form.title.edit")}</CardTitle>
        </CardHeader>
        <CardContent>
          <AffiliateForm
            mode="edit"
            initialData={affiliateData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>
    </div>
  );
}
