import { useState, useEffect, useCallback } from "react";
import { useTranslations } from "next-intl";
import { getCities } from "@/lib/admin/affiliate-api";
import { City } from "@/lib/admin/affiliate-types";
import { AppApiError } from "@/lib/api-client";

export function useCities(provinceId: number | null) {
  const t = useTranslations("admin.affiliates");
  const [data, setData] = useState<City[] | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppApiError | null>(null);

  const loadCities = useCallback(async () => {
    if (provinceId === null || provinceId === 0) {
      setData(null);
      setError(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await getCities(provinceId);
      if (result.success && result.data) {
        setData(result.data);
      } else {
        setError(new AppApiError(result.message || t("messages.error"), 0));
      }
    } catch (err) {
      console.error("Load cities error:", err);
      setError(
        err instanceof AppApiError
          ? err
          : new AppApiError(t("messages.networkError"), 0)
      );
    } finally {
      setLoading(false);
    }
  }, [provinceId, t]);

  useEffect(() => {
    loadCities();
  }, [loadCities]);

  return {
    data,
    loading,
    error,
    refetch: loadCities,
  };
}
