import { useState, useEffect, useCallback } from "react";
import { useTranslations } from "next-intl";
import { getProvinces } from "@/lib/admin/affiliate-api";
import { Province } from "@/lib/admin/affiliate-types";
import { AppApiError } from "@/lib/api-client";

export function useProvinces() {
  const t = useTranslations("admin.affiliates");
  const [data, setData] = useState<Province[] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AppApiError | null>(null);

  const loadProvinces = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getProvinces();
      if (result.success && result.data) {
        setData(result.data);
      } else {
        setError(new AppApiError(result.message || t("messages.error"), 0));
      }
    } catch (err) {
      console.error("Load provinces error:", err);
      setError(
        err instanceof AppApiError
          ? err
          : new AppApiError(t("messages.networkError"), 0)
      );
    } finally {
      setLoading(false);
    }
  }, [t]);

  useEffect(() => {
    loadProvinces();
  }, [loadProvinces]);

  return {
    data,
    loading,
    error,
    refetch: loadProvinces,
  };
}
